package org.springblade.websocket.service;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springblade.websocket.constant.TreeCacheKeyConstant;
import org.springblade.websocket.dto.tree.DeviceNode;

import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 树状态缓存机制测试
 */
@Slf4j
@SpringBootTest
public class TreeStatusCacheTest {

    @Autowired
    private TreeService treeService;

    @Autowired
    private StringRedisTemplate redisTemplate;

    /**
     * 测试状态缓存功能
     */
    @Test
    public void testStatusCaching() {
        // 清理之前的数据
        redisTemplate.delete(TreeCacheKeyConstant.STATUS_CACHE_KEY);

        // 设置构建状态
        redisTemplate.opsForValue().set(
            TreeCacheKeyConstant.BUILD_STATUS_KEY, 
            "true", 
            TreeCacheKeyConstant.BUILD_STATUS_EXPIRE_MINUTES, 
            TimeUnit.MINUTES
        );

        // 模拟设备节点
        DeviceNode deviceNode = new DeviceNode();
        deviceNode.setId("12345");
        deviceNode.setDeviceId(12345L);

        // 模拟状态更新
        treeService.updateDeviceState(deviceNode, "online", 1);
        treeService.updateDeviceState(deviceNode, "fusionState", 2);
        treeService.updateDeviceState(deviceNode, "acc", 0);

        // 验证状态缓存
        Map<Object, Object> cachedStatus = redisTemplate.opsForHash()
            .entries(TreeCacheKeyConstant.STATUS_CACHE_KEY);
        
        assert cachedStatus.size() == 3;
        assert "1".equals(cachedStatus.get("12345:online"));
        assert "2".equals(cachedStatus.get("12345:fusionState"));
        assert "0".equals(cachedStatus.get("12345:acc"));

        log.info("状态缓存测试完成，缓存的状态: {}", cachedStatus);

        // 清理测试数据
        redisTemplate.delete(TreeCacheKeyConstant.BUILD_STATUS_KEY);
        redisTemplate.delete(TreeCacheKeyConstant.STATUS_CACHE_KEY);
    }

    /**
     * 测试状态去重功能
     */
    @Test
    public void testStatusDeduplication() {
        // 清理之前的数据
        redisTemplate.delete(TreeCacheKeyConstant.STATUS_CACHE_KEY);

        // 设置构建状态
        redisTemplate.opsForValue().set(
            TreeCacheKeyConstant.BUILD_STATUS_KEY, 
            "true", 
            TreeCacheKeyConstant.BUILD_STATUS_EXPIRE_MINUTES, 
            TimeUnit.MINUTES
        );

        // 模拟设备节点
        DeviceNode deviceNode = new DeviceNode();
        deviceNode.setId("12345");
        deviceNode.setDeviceId(12345L);

        // 多次更新同一状态
        treeService.updateDeviceState(deviceNode, "online", 0);
        treeService.updateDeviceState(deviceNode, "online", 1);
        treeService.updateDeviceState(deviceNode, "online", 0);
        treeService.updateDeviceState(deviceNode, "online", 1);

        // 验证只保留最新状态
        Map<Object, Object> cachedStatus = redisTemplate.opsForHash()
            .entries(TreeCacheKeyConstant.STATUS_CACHE_KEY);
        
        assert cachedStatus.size() == 1;
        assert "1".equals(cachedStatus.get("12345:online"));

        log.info("状态去重测试完成，最终状态: {}", cachedStatus);

        // 清理测试数据
        redisTemplate.delete(TreeCacheKeyConstant.BUILD_STATUS_KEY);
        redisTemplate.delete(TreeCacheKeyConstant.STATUS_CACHE_KEY);
    }

    /**
     * 测试正常情况下不缓存状态
     */
    @Test
    public void testNormalStatusUpdate() {
        // 确保没有构建状态
        redisTemplate.delete(TreeCacheKeyConstant.BUILD_STATUS_KEY);
        redisTemplate.delete(TreeCacheKeyConstant.STATUS_CACHE_KEY);

        // 模拟设备节点
        DeviceNode deviceNode = new DeviceNode();
        deviceNode.setId("12345");
        deviceNode.setDeviceId(12345L);

        // 正常状态更新
        treeService.updateDeviceState(deviceNode, "online", 1);

        // 验证没有缓存状态
        Map<Object, Object> cachedStatus = redisTemplate.opsForHash()
            .entries(TreeCacheKeyConstant.STATUS_CACHE_KEY);
        
        assert cachedStatus.isEmpty();

        log.info("正常状态更新测试完成，没有缓存状态");
    }

    /**
     * 测试多设备状态缓存
     */
    @Test
    public void testMultiDeviceStatusCaching() {
        // 清理之前的数据
        redisTemplate.delete(TreeCacheKeyConstant.STATUS_CACHE_KEY);

        // 设置构建状态
        redisTemplate.opsForValue().set(
            TreeCacheKeyConstant.BUILD_STATUS_KEY, 
            "true", 
            TreeCacheKeyConstant.BUILD_STATUS_EXPIRE_MINUTES, 
            TimeUnit.MINUTES
        );

        // 模拟多个设备节点
        for (int i = 1; i <= 5; i++) {
            DeviceNode deviceNode = new DeviceNode();
            deviceNode.setId(String.valueOf(i));
            deviceNode.setDeviceId((long) i);

            // 更新不同设备的状态
            treeService.updateDeviceState(deviceNode, "online", i % 2);
            treeService.updateDeviceState(deviceNode, "fusionState", i % 3);
            treeService.updateDeviceState(deviceNode, "acc", i % 2);
        }

        // 验证状态缓存
        Map<Object, Object> cachedStatus = redisTemplate.opsForHash()
            .entries(TreeCacheKeyConstant.STATUS_CACHE_KEY);
        
        // 5个设备 * 3个状态字段 = 15个缓存项
        assert cachedStatus.size() == 15;

        log.info("多设备状态缓存测试完成，缓存项数量: {}", cachedStatus.size());
        log.info("缓存内容: {}", cachedStatus);

        // 清理测试数据
        redisTemplate.delete(TreeCacheKeyConstant.BUILD_STATUS_KEY);
        redisTemplate.delete(TreeCacheKeyConstant.STATUS_CACHE_KEY);
    }

    /**
     * 测试状态缓存过期时间
     */
    @Test
    public void testStatusCacheExpiration() {
        // 清理之前的数据
        redisTemplate.delete(TreeCacheKeyConstant.STATUS_CACHE_KEY);

        // 设置构建状态
        redisTemplate.opsForValue().set(
            TreeCacheKeyConstant.BUILD_STATUS_KEY, 
            "true", 
            TreeCacheKeyConstant.BUILD_STATUS_EXPIRE_MINUTES, 
            TimeUnit.MINUTES
        );

        // 模拟设备节点
        DeviceNode deviceNode = new DeviceNode();
        deviceNode.setId("12345");
        deviceNode.setDeviceId(12345L);

        // 状态更新
        treeService.updateDeviceState(deviceNode, "online", 1);

        // 检查过期时间
        Long ttl = redisTemplate.getExpire(TreeCacheKeyConstant.STATUS_CACHE_KEY);
        assert ttl > 0;

        log.info("状态缓存过期时间测试完成，TTL: {} 秒", ttl);

        // 清理测试数据
        redisTemplate.delete(TreeCacheKeyConstant.BUILD_STATUS_KEY);
        redisTemplate.delete(TreeCacheKeyConstant.STATUS_CACHE_KEY);
    }
}
