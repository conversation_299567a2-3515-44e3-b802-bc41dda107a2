package org.springblade.websocket.service;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springblade.common.enums.Operation;
import org.springblade.websocket.constant.TreeCacheKeyConstant;
import org.springblade.websocket.dto.DeviceInfo;
import org.springblade.websocket.dto.IncrementalOperation;

import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * 树增量数据缓冲机制测试
 */
@Slf4j
@SpringBootTest
public class TreeIncrementalBufferTest {

    @Autowired
    private TreeService treeService;

    @Autowired
    private StringRedisTemplate redisTemplate;

    /**
     * 测试构建状态检查
     */
    @Test
    public void testBuildingStatusCheck() {
        // 初始状态应该是未构建
        assert !treeService.isBuildingTree();
        log.info("初始构建状态: {}", treeService.isBuildingTree());

        // 模拟设置构建状态
        redisTemplate.opsForValue().set(
            TreeCacheKeyConstant.BUILD_STATUS_KEY, 
            "true", 
            TreeCacheKeyConstant.BUILD_STATUS_EXPIRE_MINUTES, 
            TimeUnit.MINUTES
        );

        // 检查状态
        assert treeService.isBuildingTree();
        log.info("设置后构建状态: {}", treeService.isBuildingTree());

        // 清理状态
        redisTemplate.delete(TreeCacheKeyConstant.BUILD_STATUS_KEY);
        assert !treeService.isBuildingTree();
        log.info("清理后构建状态: {}", treeService.isBuildingTree());
    }

    /**
     * 测试增量操作缓存
     */
    @Test
    public void testIncrementalOperationCache() {
        // 清理之前的数据
        redisTemplate.delete(TreeCacheKeyConstant.INCREMENTAL_QUEUE_KEY);

        // 创建测试数据
        DeviceInfo deviceInfo = new DeviceInfo();
        deviceInfo.setDeviceId(12345L);
        deviceInfo.setTargetId(67890L);
        deviceInfo.setOperation(Operation.INSERT);

        IncrementalOperation operation = IncrementalOperation.create("ADD", deviceInfo, "TEST");

        // 缓存操作
        treeService.cacheIncrementalOperation(operation);

        // 验证缓存
        List<String> operations = redisTemplate.opsForList().range(TreeCacheKeyConstant.INCREMENTAL_QUEUE_KEY, 0, -1);
        assert operations != null && operations.size() == 1;

        IncrementalOperation cachedOperation = JSON.parseObject(operations.get(0), IncrementalOperation.class);
        assert "ADD".equals(cachedOperation.getOperationType());
        assert cachedOperation.getDeviceInfo().getDeviceId().equals(12345L);

        log.info("缓存的操作: {}", cachedOperation);

        // 清理测试数据
        redisTemplate.delete(TreeCacheKeyConstant.INCREMENTAL_QUEUE_KEY);
    }

    /**
     * 测试并发场景下的增量操作缓存
     */
    @Test
    public void testConcurrentIncrementalOperations() throws InterruptedException {
        // 清理之前的数据
        redisTemplate.delete(TreeCacheKeyConstant.INCREMENTAL_QUEUE_KEY);

        // 设置构建状态
        redisTemplate.opsForValue().set(
            TreeCacheKeyConstant.BUILD_STATUS_KEY, 
            "true", 
            TreeCacheKeyConstant.BUILD_STATUS_EXPIRE_MINUTES, 
            TimeUnit.MINUTES
        );

        int threadCount = 10;
        int operationsPerThread = 5;
        CountDownLatch latch = new CountDownLatch(threadCount);
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);

        // 并发执行增量操作
        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            executor.submit(() -> {
                try {
                    for (int j = 0; j < operationsPerThread; j++) {
                        DeviceInfo deviceInfo = new DeviceInfo();
                        deviceInfo.setDeviceId((long) (threadId * 1000 + j));
                        deviceInfo.setTargetId((long) (threadId * 2000 + j));
                        deviceInfo.setOperation(Operation.INSERT);

                        // 这应该被缓存而不是直接执行
                        treeService.handleAdd(deviceInfo);
                    }
                } finally {
                    latch.countDown();
                }
            });
        }

        // 等待所有线程完成
        latch.await(30, TimeUnit.SECONDS);
        executor.shutdown();

        // 验证缓存的操作数量
        List<String> operations = redisTemplate.opsForList().range(TreeCacheKeyConstant.INCREMENTAL_QUEUE_KEY, 0, -1);
        assert operations != null;
        assert operations.size() == threadCount * operationsPerThread;

        log.info("并发测试完成，缓存的操作数量: {}", operations.size());

        // 清理测试数据
        redisTemplate.delete(TreeCacheKeyConstant.BUILD_STATUS_KEY);
        redisTemplate.delete(TreeCacheKeyConstant.INCREMENTAL_QUEUE_KEY);
    }

    /**
     * 测试正常情况下的增量操作（不缓存）
     */
    @Test
    public void testNormalIncrementalOperations() {
        // 确保没有构建状态
        redisTemplate.delete(TreeCacheKeyConstant.BUILD_STATUS_KEY);
        redisTemplate.delete(TreeCacheKeyConstant.INCREMENTAL_QUEUE_KEY);

        DeviceInfo deviceInfo = new DeviceInfo();
        deviceInfo.setDeviceId(99999L);
        deviceInfo.setTargetId(88888L);
        deviceInfo.setOperation(Operation.INSERT);

        // 这应该直接执行而不是缓存
        treeService.handleAdd(deviceInfo);

        // 验证没有缓存操作
        List<String> operations = redisTemplate.opsForList().range(TreeCacheKeyConstant.INCREMENTAL_QUEUE_KEY, 0, -1);
        assert operations == null || operations.isEmpty();

        log.info("正常操作测试完成，没有缓存操作");
    }
}
