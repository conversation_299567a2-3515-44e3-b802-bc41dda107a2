package org.springblade.websocket.controller;

import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.api.R;
import org.springblade.websocket.constant.TreeCacheKeyConstant;
import org.springblade.websocket.dto.IncrementalOperation;
import org.springblade.websocket.service.TreeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 树构建状态监控和管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/tree-build-status")
@Api(value = "树构建状态管理", tags = "树构建状态管理接口")
public class TreeBuildStatusController {

    @Autowired
    private TreeService treeService;

    @Autowired
    private StringRedisTemplate redisTemplate;

    /**
     * 获取树构建状态
     */
    @GetMapping("/status")
    @ApiOperation(value = "获取树构建状态")
    public R<Map<String, Object>> getBuildStatus() {
        try {
            Map<String, Object> status = new HashMap<>();
            
            // 构建状态
            boolean isBuilding = treeService.isBuildingTree();
            status.put("isBuilding", isBuilding);
            
            // 当前版本
            String currentVersion = redisTemplate.opsForValue().get(TreeCacheKeyConstant.CURRENT_VERSION_KEY);
            status.put("currentVersion", currentVersion);
            
            // 正在构建的版本
            String buildingVersion = redisTemplate.opsForValue().get(TreeCacheKeyConstant.BUILD_VERSION_KEY);
            status.put("buildingVersion", buildingVersion);
            
            // 增量操作队列长度
            Long queueLength = redisTemplate.opsForList().size(TreeCacheKeyConstant.INCREMENTAL_QUEUE_KEY);
            status.put("queueLength", queueLength != null ? queueLength : 0);
            
            // 构建状态过期时间
            Long buildStatusTtl = redisTemplate.getExpire(TreeCacheKeyConstant.BUILD_STATUS_KEY);
            status.put("buildStatusTtl", buildStatusTtl);
            
            // 队列过期时间
            Long queueTtl = redisTemplate.getExpire(TreeCacheKeyConstant.INCREMENTAL_QUEUE_KEY);
            status.put("queueTtl", queueTtl);
            
            return R.data(status);
        } catch (Exception e) {
            log.error("获取树构建状态失败", e);
            return R.fail("获取状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取增量操作队列详情
     */
    @GetMapping("/queue")
    @ApiOperation(value = "获取增量操作队列详情")
    public R<Map<String, Object>> getQueueDetails(@RequestParam(defaultValue = "0") int start,
                                                   @RequestParam(defaultValue = "10") int count) {
        try {
            Map<String, Object> result = new HashMap<>();
            
            // 队列总长度
            Long totalLength = redisTemplate.opsForList().size(TreeCacheKeyConstant.INCREMENTAL_QUEUE_KEY);
            result.put("totalLength", totalLength != null ? totalLength : 0);
            
            // 获取指定范围的操作
            List<String> operations = redisTemplate.opsForList().range(
                TreeCacheKeyConstant.INCREMENTAL_QUEUE_KEY, start, start + count - 1);
            
            if (operations != null && !operations.isEmpty()) {
                List<IncrementalOperation> parsedOperations = operations.stream()
                    .map(op -> {
                        try {
                            return JSON.parseObject(op, IncrementalOperation.class);
                        } catch (Exception e) {
                            log.error("解析增量操作失败: {}", op, e);
                            return null;
                        }
                    })
                    .filter(op -> op != null)
                    .collect(Collectors.toList());
                
                result.put("operations", parsedOperations);
            } else {
                result.put("operations", List.of());
            }
            
            result.put("start", start);
            result.put("count", count);
            
            return R.data(result);
        } catch (Exception e) {
            log.error("获取增量操作队列详情失败", e);
            return R.fail("获取队列详情失败: " + e.getMessage());
        }
    }

    /**
     * 清理增量操作队列（紧急情况使用）
     */
    @DeleteMapping("/queue")
    @ApiOperation(value = "清理增量操作队列")
    public R<String> clearQueue() {
        try {
            // 检查是否正在构建
            if (treeService.isBuildingTree()) {
                return R.fail("树正在构建中，不能清理队列");
            }
            
            Long deletedCount = redisTemplate.delete(TreeCacheKeyConstant.INCREMENTAL_QUEUE_KEY) ? 1L : 0L;
            
            log.warn("手动清理增量操作队列，删除数量: {}", deletedCount);
            return R.success("队列清理完成，删除数量: " + deletedCount);
        } catch (Exception e) {
            log.error("清理增量操作队列失败", e);
            return R.fail("清理队列失败: " + e.getMessage());
        }
    }

    /**
     * 强制清理构建状态（紧急情况使用）
     */
    @DeleteMapping("/build-status")
    @ApiOperation(value = "强制清理构建状态")
    public R<String> clearBuildStatus() {
        try {
            redisTemplate.delete(TreeCacheKeyConstant.BUILD_STATUS_KEY);
            redisTemplate.delete(TreeCacheKeyConstant.BUILD_VERSION_KEY);
            
            log.warn("手动强制清理构建状态");
            return R.success("构建状态清理完成");
        } catch (Exception e) {
            log.error("清理构建状态失败", e);
            return R.fail("清理构建状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取队列统计信息
     */
    @GetMapping("/queue/stats")
    @ApiOperation(value = "获取队列统计信息")
    public R<Map<String, Object>> getQueueStats() {
        try {
            Map<String, Object> stats = new HashMap<>();
            
            // 获取所有操作
            List<String> operations = redisTemplate.opsForList().range(
                TreeCacheKeyConstant.INCREMENTAL_QUEUE_KEY, 0, -1);
            
            if (operations != null && !operations.isEmpty()) {
                Map<String, Long> operationTypeCount = operations.stream()
                    .map(op -> {
                        try {
                            IncrementalOperation operation = JSON.parseObject(op, IncrementalOperation.class);
                            return operation.getOperationType();
                        } catch (Exception e) {
                            return "PARSE_ERROR";
                        }
                    })
                    .collect(Collectors.groupingBy(type -> type, Collectors.counting()));
                
                stats.put("operationTypeCount", operationTypeCount);
                stats.put("totalOperations", operations.size());
                
                // 计算时间范围
                long minTimestamp = Long.MAX_VALUE;
                long maxTimestamp = Long.MIN_VALUE;
                
                for (String op : operations) {
                    try {
                        IncrementalOperation operation = JSON.parseObject(op, IncrementalOperation.class);
                        minTimestamp = Math.min(minTimestamp, operation.getTimestamp());
                        maxTimestamp = Math.max(maxTimestamp, operation.getTimestamp());
                    } catch (Exception e) {
                        // 忽略解析错误
                    }
                }
                
                if (minTimestamp != Long.MAX_VALUE) {
                    stats.put("timeRange", Map.of(
                        "start", minTimestamp,
                        "end", maxTimestamp,
                        "duration", maxTimestamp - minTimestamp
                    ));
                }
            } else {
                stats.put("operationTypeCount", Map.of());
                stats.put("totalOperations", 0);
            }
            
            return R.data(stats);
        } catch (Exception e) {
            log.error("获取队列统计信息失败", e);
            return R.fail("获取统计信息失败: " + e.getMessage());
        }
    }
}
