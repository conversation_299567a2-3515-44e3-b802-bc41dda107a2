package org.springblade.websocket.dto.tree;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;

import lombok.Data;

import java.util.List;

@Data
@JsonTypeInfo(
    use = JsonTypeInfo.Id.NAME,
    include = JsonTypeInfo.As.PROPERTY,
    property = "type",
    visible = true
)
@JsonSubTypes({
    @JsonSubTypes.Type(value = DeptNode.class, name = "dept"),
    @JsonSubTypes.Type(value = DeviceTypeNode.class, name = "device_type"),
    @JsonSubTypes.Type(value = TargetNode.class, name = "target"),
    @JsonSubTypes.Type(value = DeviceNode.class, name = "device"),
    @JsonSubTypes.Type(value = ChannelNode.class, name = "channel")
})
public abstract class BaseNode {
    private String id;
	private Long deptId;
    private String name;
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private String type;
    private String parentId;
    private List<BaseNode> children;

    /**
     * 是否已展开（前端状态标识）
     */
    private boolean expanded = false;

}
