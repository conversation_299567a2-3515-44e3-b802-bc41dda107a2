package org.springblade.websocket.dto.tree;

/**
 * 树节点类型常量
 * <p>
 * 使用final class确保该类不能被继承
 */
public final class NodeType {

	/**
	 * 私有构造函数，防止实例化
	 */
	private NodeType() {
	}

	public static final String DEPT = "dept";
	public static final String DEVICE_TYPE = "device_type";
	public static final String TARGET = "target";
	public static final String DEVICE = "device";
	public static final String CHANNEL = "channel";

}
