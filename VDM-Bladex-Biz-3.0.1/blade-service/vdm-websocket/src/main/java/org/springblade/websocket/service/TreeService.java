package org.springblade.websocket.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springblade.websocket.dto.DeviceInfo;
import org.springblade.websocket.dto.tree.BaseNode;
import org.springblade.websocket.dto.tree.DeviceNode;

import java.util.List;

public interface TreeService {

	/**
	 * 构建完整的树缓存
	 */
	void buildTree(long lockSecond);

	/**
	 * 从缓存中获取完整的树结构
	 * @param rootId 根节点ID
	 * @return 节点列表
	 */
	List<BaseNode> getTree(Long rootId);


	/**
	 * 获取部门树结构（包含统计数据，不包含设备详情）
	 * 适用于首次加载，保证统计数据完整性的同时减少数据传输量
	 * @return 部门节点列表（包含统计数据）
	 */
	List<BaseNode> getDeptTreeWithStats();

	/**
	 * 获取部门树结构，并展开指定节点的全部子节点
	 * 返回全量部门树，对于expandedNodeIds中的节点，还会返回其全部子节点（包括非部门节点）
	 * @param expandedNodeIds 需要展开的节点ID列表
	 * @return 部门节点列表（包含统计数据和展开的子节点）
	 */
	List<BaseNode> getDeptTreeWithExpandedNodes(List<String> expandedNodeIds);

	/**
	 * [新增] 根据父节点ID懒加载获取其直接子节点列表
	 * @param parentKey 带前缀的父节点Redis Key
	 * @return 子节点列表
	 */
	List<BaseNode> getChildren(String parentKey);

	/**
	 * search device node by keyword
	 * @param keyword keyword
	 * @param type device type
	 * @param current current page
	 * @param pageSize page size
	 * @return Page<BaseNode>
	 */
	Page<BaseNode> searchNodes(String keyword, String type, long current, long pageSize);

	/**
	 * 根据用户权限获取部分树结构
	 * @param deptIds 用户有权限的部门ID列表
	 * @return
	 */
	List<BaseNode> getPermittedTree(List<Long> deptIds);

	/**
	 * 根据创建者ID获取其创建的终端树
	 * @param userId 创建者ID
	 * @return
	 */
	List<BaseNode> getTreeByCreator(String userId);


	/**
	 * 清除整个树缓存
	 */
	void clearCache();

	/**
	 * 检查缓存是否有效
	 * @return
	 */
	boolean isCacheValid();

	/**
	 * 获取当前缓存版本号
	 * @return
	 */
	long getCacheVersion();

	/**
	 * 根据展开节点列表动态构建树结构
	 * @param expandedNodeIds 展开的节点ID列表
	 * @return 动态构建的树结构
	 */
	List<BaseNode> getDynamicTree(List<String> expandedNodeIds);

	/**
	 * 检查节点是否有子节点
	 * @param nodeId 节点ID
	 * @return 是否有子节点
	 */
	boolean hasChildren(String nodeId);



	/**
	 * 刷新设备缓存信息
	 * @param deviceId 设备ID
	 */
	void refreshDeviceCache(String deviceId);

	/**
	 * 更新设备信息（支持增量更新）
	 * @param deviceInfo 设备信息
	 * @return 更新是否成功
	 */
	void handleUpdate(DeviceInfo deviceInfo);

	/**
	 * 处理新增
	 * @param deviceInfo
	 * @return
	 */
	void handleAdd(DeviceInfo deviceInfo);

	/**
	 * 处理删除
	 * @param deviceInfo
	 * @return
	 */
	void handleDelete(DeviceInfo deviceInfo);

	/**
	 * 处理终端绑定
	 * @param deviceInfo
	 * @return
	 */
	void handleDeviceBind(DeviceInfo deviceInfo);

	/**
	 * 处理终端解绑
	 * @param deviceInfo
	 * @return
	 */
	void handleDeviceUnbind(DeviceInfo deviceInfo);

	/**
	 * 处理导入
	 * @param deviceInfo
	 * @return
	 */
	void handleImport(DeviceInfo deviceInfo);


	/**
	 * 根据索引查询
	 * @param deviceId
	 * @return
	 */
	DeviceNode queryDeviceNodeByIndex(Long deviceId);

	/**
	 *更新设备状态
	 * @param deviceNode
	 * @param fieldOnline
	 * @param newValue
	 * @return
	 */
	boolean updateDeviceState(DeviceNode deviceNode, String fieldOnline, Integer newValue);
}
