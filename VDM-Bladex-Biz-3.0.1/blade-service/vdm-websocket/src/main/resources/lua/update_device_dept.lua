--- 更新设备部门脚本
-- 处理设备部门变更时的统计数据传播
--
-- KEYS[1]: versionedNodeKey (设备或监控对象的版本化节点键)
-- ARGV[1]: oldDeptId (原部门ID)
-- ARGV[2]: newDeptId (新部门ID)
-- ARGV[3]: currentVersion (当前版本号)
--
-- 返回值:
-- 1 表示更新成功
-- 0 表示节点不存在或参数错误
-- -1 表示出错

local versionedNodeKey = KEYS[1]
local oldDeptId = ARGV[1]
local newDeptId = ARGV[2]
local currentVersion = ARGV[3]

-- 检查参数
if not oldDeptId or not newDeptId or oldDeptId == newDeptId then
    return 0
end

-- 检查节点是否存在
if redis.call('EXISTS', versionedNodeKey) == 0 then
    return 0
end

-- 获取设备的在线状态
local onlineStatus = redis.call('HGET', versionedNodeKey, 'online')
if not onlineStatus then
    onlineStatus = "1"  -- 默认离线
end

local isOnline = tonumber(onlineStatus) == 0
local totalIncrement = 1
local onlineIncrement = isOnline and 1 or 0

-- 移除空部门节点的函数
local function removeEmptyDeptNode(deptKey, version)
    -- 获取部门ID（从key中提取）
    local deptId = string.match(deptKey, "dept_(.+)$")
    if not deptId then
        return
    end

    -- 获取父部门ID，用于从父节点的children中移除当前节点
    local parentId = redis.call('HGET', deptKey, 'parentId')

    -- 删除部门节点本身
    redis.call('DEL', deptKey)

    -- 从父节点的children中移除当前节点
    if parentId and parentId ~= 'null' and parentId ~= '' and parentId ~= '0' then
        local parentKey = "dept_" .. parentId
        local childrenKey = "tree:" .. version .. ":children:" .. parentKey
        local nodeToRemove = "dept_" .. deptId
        redis.call('ZREM', childrenKey, nodeToRemove)
    end

    -- 从根节点的children中移除（如果是顶级部门）
    if not parentId or parentId == 'null' or parentId == '' or parentId == '0' then
        local rootChildrenKey = "tree:" .. version .. ":children:dept_0"
        local nodeToRemove = "dept_" .. deptId
        redis.call('ZREM', rootChildrenKey, nodeToRemove)
    end
end

-- 更新节点的部门ID
redis.call('HSET', versionedNodeKey, 'deptId', newDeptId)

-- 从原部门减少统计数据
local function decrementDeptStats(deptId)
    local deptKey = "tree:" .. currentVersion .. ":node:dept_" .. deptId
    if redis.call('EXISTS', deptKey) == 0 then
        return false
    end

    -- 减少直属部门的selfTotal和selfOnlineNum
    redis.call('HINCRBY', deptKey, 'selfTotal', -totalIncrement)
    if isOnline then
        redis.call('HINCRBY', deptKey, 'selfOnlineNum', -onlineIncrement)
    end

    -- 向上传播减少total和onlineNum，并检查是否需要移除空节点
    local currentDeptKey = deptKey
    local nodesToRemove = {}

    while currentDeptKey do
        -- 减少统计数据
        local newTotal = redis.call('HINCRBY', currentDeptKey, 'total', -totalIncrement)
        if isOnline then
            redis.call('HINCRBY', currentDeptKey, 'onlineNum', -onlineIncrement)
        end

        -- 如果total变成0，标记为需要移除的节点
        if tonumber(newTotal) == 0 then
            table.insert(nodesToRemove, currentDeptKey)
        else
            -- 如果当前节点还有设备，则不需要继续向上检查移除
            break
        end

        -- 获取父部门
        local parentId = redis.call('HGET', currentDeptKey, 'parentId')
        if not parentId or parentId == 'null' or parentId == '' or parentId == '0' then
            break
        end

        currentDeptKey = "tree:" .. currentVersion .. ":node:dept_" .. parentId
        if redis.call('EXISTS', currentDeptKey) == 0 then
            break
        end
    end

    -- 移除空节点（从子节点开始移除）
    for i = 1, #nodesToRemove do
        local nodeToRemove = nodesToRemove[i]
        removeEmptyDeptNode(nodeToRemove, currentVersion)
    end

    return true
end

-- 向新部门增加统计数据
local function incrementDeptStats(deptId)
    local deptKey = "tree:" .. currentVersion .. ":node:dept_" .. deptId
    if redis.call('EXISTS', deptKey) == 0 then
        return false
    end

    -- 增加直属部门的selfTotal和selfOnlineNum
    redis.call('HINCRBY', deptKey, 'selfTotal', totalIncrement)
    if isOnline then
        redis.call('HINCRBY', deptKey, 'selfOnlineNum', onlineIncrement)
    end

    -- 向上传播增加total和onlineNum
    local currentDeptKey = deptKey
    while currentDeptKey do
        redis.call('HINCRBY', currentDeptKey, 'total', totalIncrement)
        if isOnline then
            redis.call('HINCRBY', currentDeptKey, 'onlineNum', onlineIncrement)
        end

        -- 获取父部门
        local parentId = redis.call('HGET', currentDeptKey, 'parentId')
        if not parentId or parentId == 'null' or parentId == '' or parentId == '0' then
            break
        end

        currentDeptKey = "tree:" .. currentVersion .. ":node:dept_" .. parentId
        if redis.call('EXISTS', currentDeptKey) == 0 then
            break
        end
    end

    return true
end

-- 执行统计数据更新
local oldSuccess = decrementDeptStats(oldDeptId)
local newSuccess = incrementDeptStats(newDeptId)

if oldSuccess and newSuccess then
    return 1
else
    return -1
end
