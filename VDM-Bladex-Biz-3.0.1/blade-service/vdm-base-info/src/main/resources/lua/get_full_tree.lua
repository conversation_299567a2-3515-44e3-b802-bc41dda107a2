-- get_full_tree.lua (v3.1 - Simplified)
-- 根据给定的根节点ID，构建一个完整的子树。
-- 只有当遍历路径上的所有父节点都是'dept'类型时，才会对'dept'节点进行'total'和'onlineNum'的递归聚合。
-- 非'dept'节点（如Category, Target）及其所有子节点的统计信息被视为静态，直接展示。

-- KEYS[1]: a single root node ID to start from (e.g., "dept_1", "category_123").
-- ARGV: (未使用)

local node_key_prefix = "tree:node:"
local children_key_prefix = "tree:children:"
-- Redis内置cjson库，无需require

local build_tree -- 声明一个前向引用

-- 递归构建树的函数
-- @param node_id: 当前要处理的节点ID
-- @param aggregate: 布尔值，指示是否应在此层级及以下进行聚合
build_tree = function(node_id, aggregate)
    local node_key = node_key_prefix .. node_id
    local node_data_flat = redis.call("HGETALL", node_key)
    if #node_data_flat == 0 then
        return nil
    end

    local node = {}
    for i = 1, #node_data_flat, 2 do
        node[node_data_flat[i]] = node_data_flat[i + 1]
    end

    -- 设备节点是叶子节点，直接返回
    if node["type"] == "device" then
        return node
    end

    local is_dept = (node["type"] == "dept")
    -- 只有当上层允许聚合且当前节点是部门时，才进行聚合计算
    local should_aggregate_this_level = aggregate and is_dept

    if should_aggregate_this_level then
        node["total"] = tonumber(node["selfTotal"]) or 0
        node["onlineNum"] = tonumber(node["selfOnlineNum"]) or 0
    end

    local children_key = children_key_prefix .. node_id
    local children_ids = redis.call("ZRANGE", children_key, 0, -1)
    if #children_ids > 0 then
        node["children"] = {}
        for _, child_id in ipairs(children_ids) do
            -- 递归构建子树，将聚合标志传递下去
            local child_subtree = build_tree(child_id, should_aggregate_this_level)
            if child_subtree then
                -- 如果当前层级需要聚合，并且子节点也是部门，则累加其统计值
                if should_aggregate_this_level and child_subtree["type"] == "dept" then
                    node["total"] = node["total"] + (tonumber(child_subtree["total"]) or 0)
                    node["onlineNum"] = node["onlineNum"] + (tonumber(child_subtree["onlineNum"]) or 0)
                end
                table.insert(node["children"], child_subtree)
            end
        end
    end

    return node
end

-- 主逻辑
local root_id = KEYS[1]

-- 脚本现在强制要求提供一个根节点ID
if not root_id or root_id == "" or root_id == "null" then
    -- redis.log(redis.LOG_WARNING, "get_full_tree.lua: root_id is required but was not provided.")
    return nil
end

-- 特殊处理虚拟根节点 "dept_0"，用于获取全树
if root_id == "dept_0" then
    local root_nodes = {}
    -- 从虚拟根节点的子集获取所有实际的根节点ID
    local root_node_ids = redis.call("ZRANGE", children_key_prefix .. "dept_0", 0, -1)

    for _, node_id in ipairs(root_node_ids) do
        -- 从实际的根节点开始构建，并启用聚合
        local root_tree = build_tree(node_id, true)
        if root_tree then
            table.insert(root_nodes, root_tree)
        end
    end
    return cjson.encode(root_nodes)
else
    -- 为任何其他指定的根节点构建子树
    -- 从根节点开始构建树，初始时允许聚合 (true)
    local full_tree = build_tree(root_id, true)
    if full_tree then
        return cjson.encode(full_tree)
    end
    return nil
end
