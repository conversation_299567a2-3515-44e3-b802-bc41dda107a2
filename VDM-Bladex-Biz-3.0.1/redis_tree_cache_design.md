### **部门终端树 Redis 缓存方案开发文档**

**版本: 4.4 (统一字段命名、增加缓存预热与对象转换机制)**
**日期: 2025-01-15**

#### **1. 架构概述**

本方案旨在为动态、多层的部门-终端树提供一个高性能、高一致性、支持复杂权限与排序过滤的 Redis 缓存层。

**核心设计原则:**

1.  **核心节点缓存 (Hash + ZSET)**: 树的核心结构（部门、终端类型、监控对象、终端）作为独立的 **Hash** 存储，其层级关系由 **Sorted Set (ZSET)** 维护，以支持灵活的排序。
2.  **终端节点状态驱动**: 在线状态 (`online`) 存在于“终端”(`device`)节点上。它的变化将通过 Lua 脚本原子性地级联更新所有父节点的统计数。
3.  **通道按需生成 (非实体化)**: **通道节点不再缓存于 Redis**。它们只在 API 请求需要时，由**后端应用层**根据“终端”(`device`)节点中的 `channelNum` 字段在内存中动态生成并附加到树中。
4.  **复杂操作原子化 (Lua)**: 所有涉及多个 Redis 读写的操作（获取树、级联更新、多重过滤）均封装在 Lua 脚本中，以保证原子性、一致性和高性能。
5.  **索引加速查找 (Set)**: 对特定字段（如创建者ID）建立倒排索引，以实现快速反向查找。

---

#### **2. Redis 数据结构详解**

**Key 命名约定:**
*   节点数据: `node:<id>`
*   子节点集合: `children:<id>` (类型: ZSET)
*   创建者索引: `user_devices:<userId>` (类型: Set)

**缓存的节点类型 (`type` 字段):**
*   `dept`: 部门
*   `device_category`: 终端类型分组
*   `target`: **监控对象**
*   `device`: **终端** (这是持有状态的、缓存结构中的叶子节点)

**数据结构示例:**

*   **`node:dept_100` (Hash)**: 一个部门节点
    | Field | Value | 描述 |
    | :--- | :--- | :--- |
    | `id` | `"dept_100"`| 节点ID |
    | `name` | `"技术部"` | 显示名称 |
    | `type` | `"dept"`| 节点类型 |
    | `parentId`| `"dept_1"` | 父节点ID |
    | `total`| `"20"` | **该部门下终端总数** |
    | `onlineNum`| `"15"` | **该部门下在线终端总数** |

*   **`node:category_1` (Hash)**: 一个终端分类节点
    | Field | Value | 描述 |
    | :--- | :--- | :--- |
    | `id` | `"category_1"`| 节点ID |
    | `name` | `"定位终端"` | 显示名称 |
    | `type` | `"device_category"`| 节点类型 |
    | `parentId`| `"dept_100"` | 父节点ID |
    | `totalCount`| `"10"` | **该分类下终端总数** |
    | `onlineNum`| `"8"` | **该分类下在线终端总数** |

*   **`node:term_jinga12345` (Hash)**: 一个监控对象节点
    | Field | Value | 描述 |
    | :--- | :--- | :--- |
    | `id` | `"term_jinga12345"`| 节点ID |
    | `name` | `"京a12345"` | 显示名称 |
    | `type` | `"target"`| 节点类型 |
    | `parentId`| `...` | 父节点ID |

*   **`node:dev_a_on_jinga` (Hash)**: 一个终端节点 (持有状态)
    | Field | Value | 描述 |
    | :--- | :--- | :--- |
    | `id` | `"dev_a_on_jinga"`| 节点ID |
    | `name` | `"终端a"` | 显示名称 |
    | `type` | `"device"` | 节点类型 |
    | `targetId` | `"term_jinga12345"` | 监控对象ID |
    | `parentId` | `"term_jinga12345"` | 父节点ID |
    | `createAccount` | `"user_123"` | 创建账户 |
    | `category` | `"1"` | 终端种类(1-定位终端,2-视频终端等) |
    | `deviceType` | `"1"` | 终端类别(1-北斗定位终端等) |
    | `deptId` | `"100"` | 所属部门ID |
    | `channelNum` | `"3"` | 视频通道个数 |
    | `online` | `"1"` | **在线状态(0-离线,1-在线)** |
    | `fusionState` | `"1"` | 融合状态(1-静止,2-运动) |
    | `uniqueId` | `"BDS001"` | 设备唯一标识码 |
    | `acc` | `"1"` | ACC状态(0-关闭,1-开启) |

---

#### **3. 核心 Lua 脚本**

**A. `get_tree.lua`**

**B. `update_device_status.lua` (更新终端状态)**
*   **功能**: 原子性地更新一个**终端**的 `online`，并级联更新所有父节点的统计字段（跳过target类型节点）。
*   **调用**: `EVAL <script> 1 <device_id> <new_online_status>`

```lua
-- update_device_status.lua
-- KEYS[1]: The ID of the device to update.
-- ARGV[1]: The new online status (0 for offline, 1 for online).

local device_id = KEYS[1]
local new_online = ARGV[1]
local device_key = 'node:' .. device_id

local old_online, parent_id = unpack(redis.call('HMGET', device_key, 'online', 'parentId'))

if not old_online or old_online == new_online then
    return 0
end

local change = (new_online == '1') and 1 or -1

redis.call('HSET', device_key, 'online', new_online)

local current_parent_id = parent_id
while current_parent_id and current_parent_id ~= 'null' and current_parent_id ~= '' do
    local parent_key = 'node:' .. current_parent_id
    local parent_type = redis.call('HGET', parent_key, 'type')

    -- 只对有统计字段的节点类型进行更新，跳过target类型
    if parent_type == 'dept' then
        redis.call('HINCRBY', parent_key, 'onlineNum', change)
    elseif parent_type == 'device_category' then
        redis.call('HINCRBY', parent_key, 'onlineNum', change)
    end

    current_parent_id = redis.call('HGET', parent_key, 'parentId')
end

return 1
```

**C. `update_device_acc_status.lua` (更新终端ACC状态)**
*   **功能**: 原子性地更新一个**终端**的 `accStatus` 字段。
*   **调用**: `EVAL <script> 1 <device_id> <new_acc_status>`

```lua
-- update_device_acc_status.lua
-- KEYS[1]: The ID of the device to update.
-- ARGV[1]: The new ACC status.

local device_id = KEYS[1]
local new_acc_status = ARGV[1]
local device_key = 'node:' .. device_id

-- 检查设备是否存在
local exists = redis.call('EXISTS', device_key)
if exists == 0 then
    return 0
end

-- 更新ACC状态
redis.call('HSET', device_key, 'accStatus', new_acc_status)

return 1
```

**D. `get_permitted_trees.lua` & `get_tree_by_creator.lua`**
*   功能和代码无变化。

---

#### **4. Kafka消息处理与增量更新**

系统通过消费Kafka消息实现增量数据更新，同时更新Redis缓存并通过WebSocket推送到前端页面。

**4.1 消息主题与处理逻辑**

**A. `device_target_change_topic` - 终端数据变化**

*   **消息内容**: 终端的新增、编辑、删除操作
*   **后端处理**:
    *   **新增**: 统计数据总数变更、Redis缓存刷新
    *   **编辑**: Redis缓存刷新
    *   **删除**: 统计数据总数变更、Redis缓存刷新
*   **前端处理**: 暂不处理

**B. `terminalstatus` - 终端状态变化**

*   **消息内容**: 终端在线/离线状态变化
*   **处理逻辑**: 需要先与原数据对比，如果状态未发生实际变化则不更新统计数据
*   **后端处理**:
    *   **上线**: 统计数据在线数变更
    *   **下线**: 统计数据在线数变更
*   **前端处理**:
    *   **上线**: 统计数据在线数变更
    *   **下线**: 统计数据在线数变更

**C. `ce.comms.fct.location.0` - 位置数据**

*   **消息内容**: 终端位置信息，包含ACC状态
*   **后端处理**: Redis终端缓存ACC状态变更
*   **前端处理**: 状态变更

**4.2 WebSocket实时推送**

所有缓存变更不仅更新Redis，还会通过WebSocket实时推送到前端页面，确保用户界面数据的实时性。

**4.3 前端接口与实现**

**A. 终端树获取接口**

前端获取终端树的接口实现在 `TreeController.java` 中：

```java
// VDM-Bladex-Biz-3.0.1/blade-service/vdm-base-info/src/main/java/com/xh/vdm/bi/controller/TreeController.java
@RestController
@RequestMapping("/tree")
public class TreeController {

    @Autowired
    private TreeService treeService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 获取部门终端树
     * 首次访问时如果Redis中没有缓存，会触发树的初始化构建
     */
    @GetMapping("/dept-device")
    public R<List<BaseNode>> getDeptDeviceTree() {
        // 检查Redis中是否已有树缓存
        String rootKey = "node:root";
        if (!redisTemplate.hasKey(rootKey)) {
            // 缓存不存在，触发树的初始化构建
            treeService.buildTree();
        }

        // 从Redis获取树结构
        List<BaseNode> tree = treeService.getTree();
        return R.data(tree);
    }

    /**
     * 强制重建树缓存
     * 用于管理员手动触发树的完全重建
     */
    @PostMapping("/rebuild")
    @PreAuthorize("hasRole('ADMIN')")
    public R<Boolean> rebuildTree() {
        treeService.buildTree();
        return R.success("树缓存重建成功");
    }
}
```

**B. WebSocket推送实现**

WebSocket推送代码位于 `vdm-websocket` 模块中，参考 `LocationTrackWebsocketConsumer.java` 的实现：

```java
// VDM-Bladex-Biz-3.0.1/blade-service/vdm-websocket/src/main/java/org/springblade/websocket/consumer/TreeChangeWebsocketConsumer.java
@Component
@Slf4j
public class TreeChangeWebsocketConsumer {

    @Autowired
    private WebSocketServer webSocketServer;

    /**
     * 消费终端状态变化消息并推送
     */
    @KafkaListener(topics = "terminalstatus", groupId = "websocket_tree_status")
    public void consumeTerminalStatus(String message) {
        try {
            log.info("接收到终端状态变化消息: {}", message);
            TerminalStatusEvent event = JSON.parseObject(message, TerminalStatusEvent.class);

            // 构建WebSocket消息
            JSONObject wsMessage = new JSONObject();
            wsMessage.put("type", "TERMINAL_STATUS_CHANGE");
            wsMessage.put("data", event);
            wsMessage.put("timestamp", System.currentTimeMillis());

            // 推送给所有连接的客户端
            webSocketServer.sendToAll(wsMessage.toJSONString());
        } catch (Exception e) {
            log.error("处理终端状态变化消息失败", e);
        }
    }

    /**
     * 消费位置数据消息并推送ACC状态变化
     */
    @KafkaListener(topics = "ce.comms.fct.location.0", groupId = "websocket_tree_acc")
    public void consumeLocationData(String message) {
        try {
            LocationEvent event = JSON.parseObject(message, LocationEvent.class);

            // 只关注ACC状态变化
            JSONObject wsMessage = new JSONObject();
            wsMessage.put("type", "TERMINAL_ACC_CHANGE");
            wsMessage.put("data", new JSONObject()
                .fluentPut("deviceId", event.getDeviceId())
                .fluentPut("accStatus", event.getAccStatus()));
            wsMessage.put("timestamp", System.currentTimeMillis());

            // 推送给所有连接的客户端
            webSocketServer.sendToAll(wsMessage.toJSONString());
        } catch (Exception e) {
            log.error("处理位置数据消息失败", e);
        }
    }
}
```

**4.4 对象转换与存储**

Redis中存储的不是对象的JSON序列化，而是将Java对象的各个字段转换成一个`Map<String, String>`。这种方式有以下优势：

1. **字段级别访问**：可以单独获取或更新对象的特定字段，无需读取整个对象
2. **内存效率**：避免了JSON序列化的额外开销
3. **原子操作**：支持Redis的HINCRBY等原子操作直接操作字段

转换示例：

```java
// Java对象到Redis Hash的转换
public Map<String, String> convertToRedisHash(BaseNode node) {
    Map<String, String> hash = new HashMap<>();
    hash.put("id", node.getId());
    hash.put("name", node.getName());
    hash.put("type", node.getType());
    hash.put("parentId", node.getParentId());

    // 根据节点类型添加特定字段
    if (node instanceof DeptNode) {
        DeptNode deptNode = (DeptNode) node;
        hash.put("total", String.valueOf(deptNode.getTotal()));
        hash.put("onlineNum", String.valueOf(deptNode.getOnlineNum()));
    } else if (node instanceof DeviceCategoryNode) {
        DeviceCategoryNode categoryNode = (DeviceCategoryNode) node;
        hash.put("totalCount", String.valueOf(categoryNode.getTotalCount()));
        hash.put("onlineNum", String.valueOf(categoryNode.getOnlineNum()));
    } else if (node instanceof DeviceNode) {
        DeviceNode deviceNode = (DeviceNode) node;
        hash.put("targetId", deviceNode.getTargetId());
        hash.put("online", String.valueOf(deviceNode.getOnline()));
        // ... 其他设备特有字段
    }

    return hash;
}

// Redis Hash到Java对象的转换
public BaseNode convertFromRedisHash(Map<String, String> hash) {
    String type = hash.get("type");
    BaseNode node = null;

    switch (type) {
        case "dept":
            node = new DeptNode();
            ((DeptNode) node).setTotal(Long.valueOf(hash.get("total")));
            ((DeptNode) node).setOnlineNum(Long.valueOf(hash.get("onlineNum")));
            break;
        case "device_category":
            node = new DeviceCategoryNode();
            ((DeviceCategoryNode) node).setTotalCount(Long.valueOf(hash.get("totalCount")));
            ((DeviceCategoryNode) node).setOnlineNum(Long.valueOf(hash.get("onlineNum")));
            break;
        case "target":
            node = new TargetNode();
            break;
        case "device":
            node = new DeviceNode();
            ((DeviceNode) node).setTargetId(hash.get("targetId"));
            ((DeviceNode) node).setOnline(Integer.valueOf(hash.get("online")));
            // ... 其他设备特有字段
            break;
    }

    // 设置通用字段
    if (node != null) {
        node.setId(hash.get("id"));
        node.setName(hash.get("name"));
        node.setType(hash.get("type"));
        node.setParentId(hash.get("parentId"));
    }

    return node;
}
```

**4.5 缓存预热机制**

为了避免冷启动问题，系统实现了缓存预热机制：

```java
@Component
public class TreeCacheWarmer {

    @Autowired
    private TreeService treeService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 应用启动时预热缓存
     */
    @EventListener(ApplicationReadyEvent.class)
    public void warmUpCache() {
        log.info("开始预热部门终端树缓存");

        // 异步执行，不阻塞应用启动
        CompletableFuture.runAsync(() -> {
            try {
                // 检查缓存是否已存在
                String rootKey = "node:root";
                if (!redisTemplate.hasKey(rootKey)) {
                    // 缓存不存在，触发构建
                    treeService.buildTree();
                    log.info("部门终端树缓存预热完成");
                } else {
                    log.info("部门终端树缓存已存在，无需预热");
                }
            } catch (Exception e) {
                log.error("部门终端树缓存预热失败", e);
            }
        });
    }

    /**
     * 定时重建缓存
     * 每天凌晨2点执行
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void rebuildCache() {
        log.info("开始定时重建部门终端树缓存");
        try {
            treeService.buildTree();
            log.info("部门终端树缓存定时重建完成");
        } catch (Exception e) {
            log.error("部门终端树缓存定时重建失败", e);
        }
    }
}
```

**4.6 数据一致性保障**

为确保树结构数据的一致性，系统采用以下策略：

1. **初始化策略**：
   - 首次访问时检查Redis缓存是否存在
   - 缓存不存在时，从数据库加载完整数据并构建树
   - 使用分布式锁防止并发重建

2. **增量更新策略**：
   - 通过Kafka消息实时捕获所有数据变化
   - 使用Lua脚本保证Redis更新的原子性
   - 级联更新确保统计数据的一致性

3. **版本控制**：
   - 树结构维护全局版本号
   - 客户端可通过版本号检测数据是否最新
   - 支持增量更新和全量更新两种模式

4. **故障恢复**：
   - 定时任务检查缓存完整性
   - 支持管理员手动触发重建
   - 异常情况下自动降级到数据库查询

---


#### **5. 典型工作流 (最终版)**
